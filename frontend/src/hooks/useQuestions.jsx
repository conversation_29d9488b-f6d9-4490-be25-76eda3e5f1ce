import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useEffect, useMemo } from 'react';
import { questionApi } from '../utils/api';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../utils/errorHandling';
import { usePersonaStream } from './usePersonaStreamMock';
import { useStrategyAssistantStore } from '../stores/strategyAssistantStore';

export const useQuestions = () => {
    const queryClient = useQueryClient();
    const queryKey = useMemo(() => ['questions'], []);

    const { setActiveQuestionId, activeQuestionId } = useStrategyAssistantStore();

    const [streamingQuestionId, setStreamingQuestionId] = useState(null);

    const personaStream = usePersonaStream();

    const questionsQuery = useQuery({
        queryKey,
        queryFn: questionApi.fetchAll,
        staleTime: 300000,
        refetchOnWindowFocus: false,
        onError: (error) => {
            toast.error(getErrorMessage(error));
        }
    });

    const addQuestionMutation = useMutation({
        mutationFn: questionApi.create,
        onMutate: async (newQuestion) => {
            await queryClient.cancelQueries({ queryKey });

            const previousQuestions = queryClient.getQueryData(queryKey);

            queryClient.setQueryData(queryKey, old => {
                return [...(old || []), newQuestion];
            });

            return { previousQuestions };
        },

        onError: (err, context) => {
            queryClient.setQueryData(queryKey, context.previousQuestions);
            toast.error(getErrorMessage(err));
        },

        onSettled: () => {
            queryClient.invalidateQueries({ queryKey });
        },
    });

    const createNewQuestion = (questionText, tagData = null) => {
        if (!questionText?.trim()) return null;

        const newId = String(Date.now());
        const newQuestion = {
            id: newId,
            text: questionText
        };

        if (tagData && tagData.tags) {
            newQuestion.tags = tagData.tags;
        }

        personaStream.resetStream();
        setActiveQuestionId(newId);
        setStreamingQuestionId(newId);

        personaStream.startStream(newId);

        addQuestionMutation.mutate(newQuestion);
        return newId;
    };

    const startStreamWithAdditionalPersonas = (questionId, additionalPersonas) => {
        console.log(`useQuestions: startStreamWithAdditionalPersonas called with questionId=${questionId}, additionalPersonas=`, additionalPersonas);

        if (!questionId) {
            console.error('useQuestions: No question ID provided to startStreamWithAdditionalPersonas');
            return;
        }

        console.log(`useQuestions: Setting streamingQuestionId to ${questionId}`);
        setStreamingQuestionId(questionId);

        console.log('useQuestions: Calling personaStream.startStream with additional personas');
        personaStream.startStream(questionId, additionalPersonas);
    };

    const updateQuestionMutation = useMutation({
        mutationFn: ({ id, data }) => questionApi.update(id, data),
        onError: (error) => {
            toast.error(getErrorMessage(error));
        }
    });

    const updateMessage = (messageId, newText) => {
        console.log('updateMessage called with:', { messageId, newText });

        if (!activeQuestionId) {
            console.error('No active question ID');
            return;
        }

        const isCurrentlyStreaming = streamingQuestionId === activeQuestionId;

        const currentQuestion = queryClient.getQueryData(queryKey)?.find(q => q.id === activeQuestionId);
        let messageToEdit = null;
        let isInterviewerMessage = false;

        if (isCurrentlyStreaming && personaStream.thoughts.length > 0) {
            messageToEdit = personaStream.thoughts.find(thought => thought.id === messageId);
            isInterviewerMessage = messageToEdit?.name === "インタビュアー";
        }

        if (!messageToEdit && currentQuestion?.personas) {
            messageToEdit = currentQuestion.personas.find(persona => persona.id === messageId);
            isInterviewerMessage = messageToEdit?.name === "インタビュアー";
        }

        console.log('Message edit details:', {
            isCurrentlyStreaming,
            isInterviewerMessage,
            messageToEdit,
            streamingQuestionId,
            activeQuestionId
        });

        if (isInterviewerMessage && personaStream.startStreamFromMessage) {
            console.log('Triggering new stream from edited interviewer message');

            if (!isCurrentlyStreaming) {
                setStreamingQuestionId(activeQuestionId);
            }

            const storedPersonas = currentQuestion?.personas || [];
            personaStream.startStreamFromMessage(activeQuestionId, messageId, newText, storedPersonas);
            return;
        }

        queryClient.setQueryData(queryKey, oldQuestions => {
            if (!oldQuestions) return oldQuestions;

            return oldQuestions.map(question => {
                if (question.id === activeQuestionId && question.personas) {
                    return {
                        ...question,
                        personas: question.personas.map(persona =>
                            persona.id === messageId
                                ? { ...persona, thought: newText }
                                : persona
                        )
                    };
                }
                return question;
            });
        });

        if (currentQuestion && currentQuestion.personas) {
            const updatedPersonas = currentQuestion.personas.map(persona =>
                persona.id === messageId
                    ? { ...persona, thought: newText }
                    : persona
            );

            updateQuestionMutation.mutate({
                id: activeQuestionId,
                data: { personas: updatedPersonas }
            }, {
                onSuccess: () => {
                    toast.success('メッセージが更新されました');
                },
                onError: (error) => {
                    console.error('Failed to update message:', error);
                    toast.error('メッセージの更新に失敗しました');
                    queryClient.invalidateQueries({ queryKey });
                }
            });
        }
    };

    useEffect(() => {
        if (personaStream.isComplete && streamingQuestionId) {
            console.log('Stream completed for question ID:', streamingQuestionId);

            const personas = personaStream.thoughts.map(thought => ({
                id: thought.id,
                name: thought.name,
                thought: thought.thought
            }));

            const currentQuestion = queryClient.getQueryData(queryKey)?.find(q => q.id === streamingQuestionId);

            const uniquePersonaNames = [...new Set(
                personaStream.thoughts
                    .filter(thought => thought.name !== "インタビュアー")
                    .map(thought => thought.name)
            )];

            console.log('Unique persona names from thoughts:', uniquePersonaNames);

            const updatedInterviewPlan = personaStream.interviewPlan ||
                (currentQuestion?.interviewplan ? { ...currentQuestion.interviewplan } : { targetpersonas: [] });

            if (!updatedInterviewPlan.targetpersonas) {
                updatedInterviewPlan.targetpersonas = [];
            }

            uniquePersonaNames.forEach(personaName => {
                if (!updatedInterviewPlan.targetpersonas.includes(personaName)) {
                    updatedInterviewPlan.targetpersonas.push(personaName);
                }
            });

            console.log('Updated interview plan with all personas:', updatedInterviewPlan);

            const updatedQuestionData = {
                interviewplan: updatedInterviewPlan,
                personas: personas,
                strategies: personaStream.strategy ? [personaStream.strategy] : [],
                experts: personaStream.prospections
            };

            console.log('Saving updated data to database:', updatedQuestionData);

            queryClient.setQueryData(queryKey, oldQuestions => {
                if (!oldQuestions) return oldQuestions;

                return oldQuestions.map(question => {
                    if (question.id === streamingQuestionId) {
                        return {
                            ...question,
                            ...updatedQuestionData
                        };
                    }
                    return question;
                });
            });

            updateQuestionMutation.mutate({
                id: streamingQuestionId,
                data: updatedQuestionData
            }, {
                onSuccess: () => {
                    toast.success('インタビューデータが保存されました');
                }
            });

            setStreamingQuestionId(null);
        }
    }, [personaStream.isComplete, streamingQuestionId, queryClient, queryKey, personaStream.interviewPlan, personaStream.thoughts, personaStream.strategy, personaStream.prospections, updateQuestionMutation]);

    return {
        questions: questionsQuery.data || [],
        isLoading: questionsQuery.isPending,
        isError: questionsQuery.isError,
        error: questionsQuery.error,
        createNewQuestion,
        isAddingQuestion: addQuestionMutation.isPending,
        addQuestionMutation,
        streamingQuestionId,
        interviewPlan: personaStream.interviewPlan,
        personaThoughts: personaStream.thoughts,
        personaStrategy: personaStream.strategy,
        expertProspections: personaStream.prospections,
        streamStatus: personaStream.status,
        isStreamLoading: personaStream.isLoading,
        isStreamComplete: personaStream.isComplete,
        isStreamError: personaStream.isError,
        interviewPlanLoading: personaStream.interviewPlanLoading,
        thoughtsLoading: personaStream.thoughtsLoading,
        prospectionsLoading: personaStream.prospectionsLoading,
        resetPersonaStream: personaStream.resetStream,
        startStream: personaStream.startStream,
        startStreamWithAdditionalPersonas,
        updateQuestion: (id, data) => updateQuestionMutation.mutate({ id, data }),
        updateMessage
    };
};