import { useState, useEffect, useRef, useCallback } from 'react';
import { useStrategyAssistantStore } from '../stores/strategyAssistantStore';

export const usePersonaStream = () => {
    const { activeQuestionId } = useStrategyAssistantStore();
    const [interviewPlan, setInterviewPlan] = useState(null);
    const [thoughts, setThoughts] = useState([]);
    const [strategy, setStrategy] = useState(null);
    const [prospections, setProspections] = useState([]);
    const [status, setStatus] = useState('idle');
    const [interviewPlanLoading, setInterviewPlanLoading] = useState(false);
    const [thoughtsLoading, setThoughtsLoading] = useState(false);
    const [prospectionsLoading, setProspectionsLoading] = useState(false);
    const eventSourceRef = useRef(null);
    const thoughtsRef = useRef([]);

    useEffect(() => {
        thoughtsRef.current = thoughts;
    }, [thoughts]);

    const resetStream = useCallback(() => {
        setInterviewPlan(null);
        setThoughts([]);
        setStrategy(null);
        setProspections([]);
        setStatus('idle');
        setInterviewPlanLoading(false);
        setThoughtsLoading(false);
        setProspectionsLoading(false);

        if (eventSourceRef.current) {
            eventSourceRef.current.close();
            eventSourceRef.current = null;
        }
        thoughtsRef.current = [];
    }, []);

    const startStream = useCallback((newQuestionId, additionalPersonas = []) => {
        const id = newQuestionId || activeQuestionId;

        if (!id) {
            console.error('No question ID provided to usePersonaStream');
            setStatus('error');
            return;
        }

        const isAddingPersonas = additionalPersonas.length > 0;
        console.log(`usePersonaStream: startStream called with id=${id}, additionalPersonas=`, additionalPersonas);
        console.log(`usePersonaStream: isAddingPersonas=${isAddingPersonas}`);

        if (!isAddingPersonas) {
            console.log('usePersonaStream: Resetting stream for new question');
            resetStream();
        } else {
            console.log('usePersonaStream: Preparing for additional personas - preserving existing chat history');
            if (eventSourceRef.current) {
                console.log('usePersonaStream: Closing existing event source before starting additional personas stream');
                eventSourceRef.current.close();
                eventSourceRef.current = null;
            }
            setStatus('loading');
            setThoughtsLoading(true);
            setProspectionsLoading(false);
            setProspections([]);
            console.log('usePersonaStream: Cleared existing prospections for replacement with updated analysis');
        }

        setStatus('loading');
        if (!isAddingPersonas) {
            setInterviewPlanLoading(true);
        }

        let streamUrl = `http://localhost:5001/api/stream/question/${id}`;
        if (isAddingPersonas) {
            const params = new URLSearchParams();
            additionalPersonas.forEach(persona => params.append('additionalPersonas', persona));

            const existingPersonaNames = [...new Set(
                thoughtsRef.current
                    .filter(thought => thought.name !== "インタビュアー")
                    .map(thought => thought.name)
            )];
            if (existingPersonaNames.length > 0) {
                params.append('existingPersonas', JSON.stringify(existingPersonaNames));
            }

            streamUrl += `?${params.toString()}`;
            console.log('usePersonaStream: Streaming URL with additional personas:', streamUrl);
        }

        const eventSource = new EventSource(streamUrl);
        eventSourceRef.current = eventSource;

        eventSource.onopen = () => {
            console.log('SSE connection opened');
        };

        eventSource.addEventListener('interviewplan', (event) => {
            const plan = JSON.parse(event.data);
            if (!isAddingPersonas) {
                setInterviewPlan(plan);
                setInterviewPlanLoading(false);
                setThoughtsLoading(true);
            }
        });

        eventSource.addEventListener('thought', (event) => {
            const thought = JSON.parse(event.data);
            console.log('usePersonaStream: Received thought:', thought);
            setThoughts(prev => [...prev, thought]);
        });

        eventSource.addEventListener('strategy', (event) => {
            const strategy = JSON.parse(event.data);
            setStrategy(strategy);
            setThoughtsLoading(false);
            setProspectionsLoading(true);
        });

        eventSource.addEventListener('prospection', (event) => {
            const prospection = JSON.parse(event.data);
            console.log('usePersonaStream: Received prospection:', prospection);
            setProspections(prev => [...prev, prospection]);
        });

        eventSource.addEventListener('complete', () => {
            console.log('usePersonaStream: Stream completed');
            setStatus('completed');
            setThoughtsLoading(false);
            setProspectionsLoading(false);
            eventSource.close();
            eventSourceRef.current = null;
        });

        eventSource.onerror = (error) => {
            console.error('SSE error:', error);
            setStatus('error');
            setInterviewPlanLoading(false);
            setThoughtsLoading(false);
            setProspectionsLoading(false);
            eventSource.close();
            eventSourceRef.current = null;
        };
    }, [activeQuestionId, resetStream]);

    const startStreamFromMessage = useCallback((newQuestionId, editedMessageId, newMessageText, storedPersonas = []) => {
        const id = newQuestionId || activeQuestionId;

        if (!id) {
            console.error('No question ID provided to usePersonaStream');
            setStatus('error');
            return;
        }

        console.log(`usePersonaStream: startStreamFromMessage called with id=${id}, editedMessageId=${editedMessageId}, newMessageText=${newMessageText}`);
        console.log('Current thoughts:', thoughts);
        console.log('Stored personas:', storedPersonas);

        if (eventSourceRef.current) {
            eventSourceRef.current.close();
            eventSourceRef.current = null;
        }

        const sourceMessages = thoughts.length > 0 ? thoughts : storedPersonas;
        console.log('Using source messages:', sourceMessages);

        const editedMessageIndex = sourceMessages.findIndex(msg => msg.id === editedMessageId);
        if (editedMessageIndex === -1) {
            console.error('Edited message not found in source messages');
            console.log('Looking for ID:', editedMessageId);
            console.log('Available IDs:', sourceMessages.map(msg => ({ id: msg.id, name: msg.name })));
            return;
        }

        console.log(`Found edited message at index ${editedMessageIndex}`);

        const updatedThoughts = sourceMessages.slice(0, editedMessageIndex + 1).map(msg => ({
            id: msg.id,
            type: "persona",
            name: msg.name,
            thought: msg.id === editedMessageId ? newMessageText : msg.thought
        }));

        setThoughts(updatedThoughts);
        setProspections([]);
        setStatus('loading');
        setThoughtsLoading(true);
        setProspectionsLoading(false);

        const streamUrl = `http://localhost:5001/api/stream/question/${id}/edit`;

        const requestBody = {
            editedMessageId: editedMessageId,
            newMessageText: newMessageText,
            conversationHistory: updatedThoughts
        };

        console.log('usePersonaStream: Starting edit stream with URL:', streamUrl);
        console.log('usePersonaStream: Request body:', requestBody);

        fetch(streamUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream',
            },
            body: JSON.stringify(requestBody)
        }).then(response => {
            console.log('usePersonaStream: Received response:', response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            const readStream = async () => {
                try {
                    let buffer = '';

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) {
                            console.log('usePersonaStream: Stream ended');
                            break;
                        }

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');

                        buffer = lines.pop() || '';

                        let currentEventType = null;

                        for (const line of lines) {
                            console.log('usePersonaStream: Processing line:', line);

                            if (line.startsWith('event:')) {
                                currentEventType = line.substring(6).trim();
                                console.log('usePersonaStream: Event type:', currentEventType);
                                continue;
                            }

                            if (line.startsWith('data:')) {
                                const data = line.substring(5).trim();
                                console.log('usePersonaStream: Data:', data);

                                if (data && currentEventType) {
                                    try {
                                        const parsedData = JSON.parse(data);
                                        console.log('usePersonaStream: Parsed data:', parsedData);

                                        if (currentEventType === 'thought') {
                                            console.log('usePersonaStream: Adding thought:', parsedData);
                                            setThoughts(prev => [...prev, parsedData]);
                                        } else if (currentEventType === 'prospection') {
                                            console.log('usePersonaStream: Adding prospection:', parsedData);
                                            setProspections(prev => [...prev, parsedData]);
                                        } else if (currentEventType === 'complete') {
                                            console.log('usePersonaStream: Stream completed');
                                            setStatus('completed');
                                            setThoughtsLoading(false);
                                            setProspectionsLoading(false);
                                            return;
                                        }

                                        currentEventType = null;
                                    } catch (e) {
                                        console.error('usePersonaStream: Error parsing SSE data:', e, 'Data:', data);
                                    }
                                }
                            }

                            if (line === '') {
                                currentEventType = null;
                            }
                        }
                    }
                } catch (error) {
                    console.error('usePersonaStream: Error reading stream:', error);
                    setStatus('error');
                    setThoughtsLoading(false);
                    setProspectionsLoading(false);
                }
            };

            readStream();
        }).catch(error => {
            console.error('usePersonaStream: Error starting edit stream:', error);
            setStatus('error');
            setThoughtsLoading(false);
            setProspectionsLoading(false);
        });

    }, [activeQuestionId, thoughts]);

    useEffect(() => {
        return () => {
            if (eventSourceRef.current) {
                eventSourceRef.current.close();
            }
        };
    }, []);

    return {
        thoughts,
        strategy,
        prospections,
        interviewPlan,
        status,
        isLoading: status === 'loading',
        isComplete: status === 'completed',
        isError: status === 'error',
        interviewPlanLoading,
        thoughtsLoading,
        prospectionsLoading,
        startStream,
        startStreamFromMessage,
        resetStream
    };
};
