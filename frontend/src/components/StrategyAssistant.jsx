import { useState, useEffect, useMemo } from 'react';
import Sidebar from './Sidebar';
import MainContent from './MainContent';
import { useQuestions } from '../hooks/useQuestions';
import NewQuestionForm from './NewQuestionForm';
import { useStrategyAssistantStore } from '../stores/strategyAssistantStore';

const StrategyAssistant = () => {
    const {
        questions,
        isLoading,
        isError,
        error,
        createNewQuestion,
        addQuestionMutation,
        streamingQuestionId,
        interviewPlan,
        personaThoughts,
        expertProspections,
        isStreamLoading,
        isStreamComplete,
        interviewPlanLoading,
        thoughtsLoading,
        prospectionsLoading,
        startStreamWithAdditionalPersonas,
        updateQuestion,
        updateMessage
    } = useQuestions();

    const activeQuestionId = useStrategyAssistantStore((state) => state.activeQuestionId);
    const setActiveQuestionId = useStrategyAssistantStore((state) => state.setActiveQuestionId);
    const [isAddingNewQuestion, setIsAddingNewQuestion] = useState(false);
    const [localTargetPersonas, setLocalTargetPersonas] = useState([]);

    const sortedQuestions = useMemo(() => {
        if (!questions || questions.length === 0) return [];
        return [...questions].sort((a, b) => {
            const idA = parseInt(a?.id, 10) || 0;
            const idB = parseInt(b?.id, 10) || 0;
            return idB - idA;
        });
    }, [questions]);

    const activeQuestionData = useMemo(() => {
        if (isAddingNewQuestion) {
            return null;
        }
        if (activeQuestionId === null) {
            return null;
        }
        return questions.find(q => q.id === activeQuestionId);
    }, [activeQuestionId, questions, isAddingNewQuestion]);

    useEffect(() => {
        if (addQuestionMutation.isSuccess && addQuestionMutation.data?.id) {
            if (isAddingNewQuestion) {
                console.log("New question added, setting active ID:", addQuestionMutation.data.id);
                setActiveQuestionId(addQuestionMutation.data.id);
                setIsAddingNewQuestion(false);
            }
            addQuestionMutation.reset();
        }
    }, [addQuestionMutation.isSuccess, addQuestionMutation.data, isAddingNewQuestion, setActiveQuestionId, addQuestionMutation]);

    useEffect(() => {
        if (isLoading || isAddingNewQuestion || !questions || questions.length === 0) {
            return;
        }

        const questionExists = activeQuestionId !== null && questions.some(q => q.id === activeQuestionId);

        if (activeQuestionId !== null && !questionExists) {
            console.log(`Active ID ${activeQuestionId} not found. Resetting to latest.`);
            const latestQuestionId = sortedQuestions[0]?.id;
            setActiveQuestionId(latestQuestionId || null);
        }
        else if (activeQuestionId === null && questions.length > 0) {
            console.log("Active ID is null, setting to latest.");
            const latestQuestionId = sortedQuestions[0]?.id;
            if (latestQuestionId) {
                setActiveQuestionId(latestQuestionId);
            }
        }
    }, [questions, activeQuestionId, sortedQuestions, setActiveQuestionId, isLoading, isAddingNewQuestion]);

    const handleQuestionClick = (id) => {
        if (isAddingNewQuestion) {
            setIsAddingNewQuestion(false);
        }
        setActiveQuestionId(id);
    };

    const addNewQuestion = () => {
        setIsAddingNewQuestion(true);
        setActiveQuestionId(null);
    };

    const handleNewQuestionSubmit = async (questionText, tagData) => {
        const trimmedText = questionText ? questionText.trim() : '';
        if (trimmedText) {
            try {
                console.log("Submitting new question with tags:", tagData);
                createNewQuestion(trimmedText, tagData);
            } catch (err) {
                console.error("Failed to create new question:", err);
                setIsAddingNewQuestion(false);
                const latestQuestionId = sortedQuestions[0]?.id;
                setActiveQuestionId(latestQuestionId || null);
            }
        } else {
            console.warn("Attempted to submit an empty question. Cancelling.");
            setIsAddingNewQuestion(false);
            const latestQuestionId = sortedQuestions[0]?.id;
            setActiveQuestionId(latestQuestionId || null);
        }
    };

    const handleCancelNewQuestion = () => {
        console.log("Cancelling add new question.");
        setIsAddingNewQuestion(false);
        const latestQuestionId = sortedQuestions[0]?.id;
        setActiveQuestionId(latestQuestionId || null);
    };

    // Effect to initialize localTargetPersonas when active question changes
    useEffect(() => {
        if (activeQuestionId) {
            const activeQuestion = questions.find(q => q.id === activeQuestionId);
            if (activeQuestion?.interviewplan?.targetpersonas) {
                setLocalTargetPersonas(activeQuestion.interviewplan.targetpersonas);
            } else if (interviewPlan?.targetpersonas) {
                setLocalTargetPersonas(interviewPlan.targetpersonas);
            } else {
                setLocalTargetPersonas([]);
            }
        } else {
            setLocalTargetPersonas([]);
        }
    }, [activeQuestionId, questions, interviewPlan]);

    const handlePersonasAdded = (newPersonaNames) => {
        console.log("StrategyAssistant: handlePersonasAdded called with:", newPersonaNames);

        if (!activeQuestionId) {
            console.error("StrategyAssistant: No activeQuestionId found, cannot add personas");
            return;
        }

        console.log(`StrategyAssistant: Active question ID: ${activeQuestionId}`);
        console.log(`StrategyAssistant: Current streaming question ID: ${streamingQuestionId}`);

        const currentInterviewPlan = interviewPlan ||
            questions.find(q => q.id === activeQuestionId)?.interviewplan;

        if (!currentInterviewPlan) {
            console.error("StrategyAssistant: No interview plan found for the active question");
            return;
        }

        console.log("StrategyAssistant: Current interview plan:", currentInterviewPlan);
        console.log("StrategyAssistant: Current target personas:", currentInterviewPlan.targetpersonas);

        const updatedTargetPersonas = [
            ...(currentInterviewPlan.targetpersonas || []),
            ...newPersonaNames
        ];

        console.log("StrategyAssistant: Updated target personas:", updatedTargetPersonas);

        setLocalTargetPersonas(updatedTargetPersonas);

        const updatedInterviewPlan = {
            ...currentInterviewPlan,
            targetpersonas: updatedTargetPersonas
        };

        console.log("StrategyAssistant: Updating interview plan in database:", updatedInterviewPlan);

        updateQuestion(activeQuestionId, {
            interviewplan: updatedInterviewPlan
        });

        setTimeout(() => {
            console.log("StrategyAssistant: Restarting stream with updated personas:", newPersonaNames);
            startStreamWithAdditionalPersonas(activeQuestionId, newPersonaNames);
        }, 500);
    };

    if (isLoading) {
        return <div className="flex h-screen items-center justify-center">Loading Strategy Assistant...</div>;
    }

    if (isError) {
        return (
            <div className="flex h-screen items-center justify-center">
                <div className="text-red-500">Error loading questions: {error instanceof Error ? error.message : String(error)}</div>
            </div>
        );
    }

    if (!isAddingNewQuestion && questions.length === 0) {
        return (
            <div className="flex flex-col md:flex-row h-screen w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                <Sidebar
                    sortedQuestions={[]}
                    activeQuestionId={null}
                    handleQuestionClick={() => { }}
                    addNewQuestion={addNewQuestion}
                    isAddingNewQuestion={isAddingNewQuestion}
                />
                <div className="flex-1 flex items-center justify-center p-6 bg-gray-50">
                    No questions yet. Click "Add New Question" to begin.
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col md:flex-row h-screen w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
            <Sidebar
                sortedQuestions={sortedQuestions}
                activeQuestionId={activeQuestionId}
                handleQuestionClick={handleQuestionClick}
                addNewQuestion={addNewQuestion}
                isAddingNewQuestion={isAddingNewQuestion}
            />

            {isAddingNewQuestion ? (
                <NewQuestionForm
                    onSubmit={handleNewQuestionSubmit}
                    onCancel={handleCancelNewQuestion}
                />
            ) : activeQuestionData ? (
                <MainContent
                    activeQuestion={activeQuestionData}
                    interviewPlan={activeQuestionId === streamingQuestionId ? interviewPlan : null}
                    personaThoughts={activeQuestionId === streamingQuestionId ? personaThoughts : []}
                    expertProspections={activeQuestionId === streamingQuestionId ? expertProspections : []}
                    isStreamLoading={activeQuestionId === streamingQuestionId && isStreamLoading}
                    isStreamComplete={activeQuestionId === streamingQuestionId && isStreamComplete}
                    interviewPlanLoading={activeQuestionId === streamingQuestionId && interviewPlanLoading}
                    thoughtsLoading={activeQuestionId === streamingQuestionId && thoughtsLoading}
                    prospectionsLoading={activeQuestionId === streamingQuestionId && prospectionsLoading}
                    onPersonasAdded={handlePersonasAdded}
                    targetPersonas={localTargetPersonas}
                    onEditMessage={updateMessage}
                />
            ) : (
                <div className="flex-1 flex items-center justify-center p-6 bg-gray-50">
                    {questions.length > 0 ? "Select a question from the list." : "Loading..."}
                </div>
            )}
        </div>
    );
};

export default StrategyAssistant;
