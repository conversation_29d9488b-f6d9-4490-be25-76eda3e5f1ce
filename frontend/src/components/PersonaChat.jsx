import { useMemo, useCallback, useRef, useEffect } from 'react';
import { v7 as uuidv7 } from 'uuid';
import ThoughtBubble from './ThoughtBubble';
import ChatPersonaHeader from './ChatPersonaHeader';

const PersonaChat = ({ thoughts, isLoading, isComplete, targetPersonas = [], onPersonasAdded, onEditMessage }) => {
    const chatContainerRef = useRef(null);
    const personaColors = useMemo(() => {
        const colorOptions = ['blue', 'cyan', 'purple', 'green', 'pink', 'orange', 'lightblue', 'lightpurple', 'lightcyan', 'lightpink', 'lightorange', 'lightgreen'];
        const nameToColor = {};
        let colorIndex = 0;

        (thoughts || []).forEach(thought => {
            if (!nameToColor[thought.name]) {
                nameToColor[thought.name] = colorOptions[colorIndex % colorOptions.length];
                colorIndex++;
            }
        });

        return nameToColor;
    }, [thoughts]);

    const renderMessages = useCallback(() => {
        return (thoughts || []).map((thought, index) => {
            const personaColor = personaColors[thought.name];
            const key = thought.id || `thought-${index}-${uuidv7()}`;

            return (
                <div key={key} className="w-full mb-3">
                    <ThoughtBubble
                        text={thought.thought}
                        color={personaColor}
                        personaName={thought.name}
                        isComplete={isComplete}
                        onEdit={onEditMessage}
                        thoughtId={thought.id}
                    />
                </div>
            );
        });
    }, [thoughts, personaColors, isComplete, onEditMessage]);

    useEffect(() => {
        if (chatContainerRef.current && thoughts && thoughts.length > 0) {
            setTimeout(() => {
                const scrollContainer = chatContainerRef.current;
                if (scrollContainer) {
                    scrollContainer.scrollTop = scrollContainer.scrollHeight;
                }
            }, 100);
        }
    }, [thoughts]);

    useEffect(() => {
        if (chatContainerRef.current) {
            const scrollContainer = chatContainerRef.current;
            scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
    }, [isLoading]);

    useEffect(() => {
        if (chatContainerRef.current) {
            const scrollContainer = chatContainerRef.current;
            scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
    }, []);

    return (
        <div className="bg-white border border-gray-200 rounded-lg relative w-full h-full max-h-[calc(100vh-280px)] flex flex-col">
            <div ref={chatContainerRef} className="w-full h-full overflow-y-auto flex flex-col">
                {/* Show header if we have targetPersonas or thoughts */}
                {((targetPersonas && targetPersonas.length > 0) || (thoughts && thoughts.length > 0)) && (
                    <ChatPersonaHeader
                        thoughts={thoughts}
                        targetPersonas={targetPersonas}
                        onPersonasAdded={onPersonasAdded}
                    />
                )}

                <div className="p-2 md:p-4 lg:p-6">
                    {renderMessages()}

                    {isLoading && !isComplete && (
                        <div className="mt-4 text-blue-500 animate-pulse text-center">
                            インタビュー中...
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PersonaChat;
