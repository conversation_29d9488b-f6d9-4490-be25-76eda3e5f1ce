import { useMemo } from 'react';
import ThoughtBubble from './ThoughtBubble';

const PersonaThoughts = ({ personas }) => {
    const personaColors = useMemo(() => {
        if (!personas || !Array.isArray(personas)) return {};

        const colorOptions = ['blue', 'cyan', 'purple', 'green', 'pink', 'orange', 'lightblue', 'lightpurple', 'lightcyan', 'lightpink', 'lightorange', 'lightgreen'  ];
        const nameToColor = {};
        let colorIndex = 0;

        personas.forEach(p => {
            if (!nameToColor[p.name]) {
                nameToColor[p.name] = colorOptions[colorIndex % colorOptions.length];
                colorIndex++;
            }
        });

        return nameToColor;
    }, [personas]);


    const positions = useMemo(() => {
        if (!personas || !Array.isArray(personas)) return [];

        const fixedPositions = [
            // Top-left
            { top: '20%', left: '15%' },
            // Top-center
            { top: '20%', left: '50%' },
            // Top-right
            { top: '20%', left: '85%' },
            // Left-center
            { top: '50%', left: '15%' },
            // Right-center
            { top: '50%', left: '85%' },
            // Bottom-left
            { top: '80%', left: '15%' },
            // Bottom-center
            { top: '80%', left: '50%' },
            // Bottom-right
            { top: '80%', left: '85%' },
        ];

        return personas.map((persona, index) => ({
            id: persona.id,
            top: fixedPositions[index % fixedPositions.length].top,
            left: fixedPositions[index % fixedPositions.length].left
        }));
    }, [personas]);

    if (!personas || personas.length === 0) {
        return null;
    }

    return (
        <div className="absolute w-full h-full">
            {personas.slice(0, 8).map((persona, index) => {
                const personaColor = personaColors[persona.name];
                const position = positions.find(pos => pos.id === persona.id);
                if (!position) return null; // 8個以上あった場合の保険
                return (
                    <div key={persona.id} className="absolute" style={{
                        top: position.top,
                        left: position.left,
                        transform: 'translate(-50%, -50%)',
                        maxWidth: '30%'
                    }}>
                        <ThoughtBubble
                            text={persona.thought}
                            color={personaColor}
                            personaName={persona.name}
                            customClassName={index === 1 ? 'my-24' : 'my-16'}
                        />
                    </div>
                );
            })}
        </div>
    );
};

export default PersonaThoughts;
