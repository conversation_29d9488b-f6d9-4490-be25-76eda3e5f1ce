import { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import { v7 as uuidv7 } from 'uuid';
import ExpertIcon from './ExpertIcon';

const StrategySection = ({ experts, isLoading }) => {
    const expertsWithIds = useMemo(() => {
        if (!experts || experts.length === 0) return [];

        return experts.map(expert => {
            if (expert.id) return expert;
            return { ...expert, id: uuidv7() };
        });
    }, [experts]);

    return (
        <div className="bg-white border border-gray-200 rounded-lg p-2 md:p-3 relative w-full h-full max-h-[calc(100vh-280px)] flex flex-col">
            <h3 className="text-base font-medium mb-2">施策提案</h3>
            <div className="flex-1 overflow-y-auto">
                {expertsWithIds.map((expert, index) => (
                    <div key={expert.id || `expert-${index}`}>
                        <div className="flex items-center mb-1">
                            <ExpertIcon
                                title={expert.title}
                                className="mr-2"
                                size="small"
                            />
                            <h4 className="text-sm font-bold flex-1">{expert.title}</h4>
                        </div>
                        <div className="text-sm">
                            <ReactMarkdown>{expert.comment}</ReactMarkdown>
                        </div>
                        {index < expertsWithIds.length - 1 && <hr className="border-t border-gray-200 my-1" />}
                    </div>
                ))}

                {isLoading && (
                    <div className="mt-2 text-blue-500 animate-pulse text-center">
                        施策分析中...
                    </div>
                )}

                {!isLoading && expertsWithIds.length === 0 && (
                    <div className="text-gray-500 text-center py-4">
                        施策提案はまだありません
                    </div>
                )}
            </div>
        </div>
    );
};

export default StrategySection;