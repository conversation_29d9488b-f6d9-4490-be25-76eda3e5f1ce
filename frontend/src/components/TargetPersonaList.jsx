import PersonaProfileTooltip from './PersonaProfileTooltip';
import { getPersonaAvatar } from '../utils/iconMappings';

const TargetPersonaList = ({ personas }) => {
    if (!personas || personas.length === 0) {
        return null;
    }

    return (
        <div className="mt-3">
            <div className="flex flex-wrap gap-2">
                {personas.map((persona, index) => {
                    return (
                        <PersonaProfileTooltip
                            key={`${persona}-${index}`}
                            personaName={persona}
                        >
                            <div
                                className="flex items-center gap-1 bg-blue-50 px-3 py-1 rounded-full shadow-sm cursor-pointer hover:shadow"
                            >
                                <img src={getPersonaAvatar(persona)} alt={persona} className="w-5 h-5 rounded-full" />
                                <span className="text-sm font-medium">{persona}</span>
                            </div>
                        </PersonaProfileTooltip>
                    );
                })}
            </div>
        </div>
    );
};

export default TargetPersonaList;
