import PersonaChat from './PersonaChat';
import StrategySection from './StrategySection';
import InterviewPlan from './InterviewPlan';
import { CalendarDays, Sun, Clock, Tag } from "lucide-react";

const TagDisplay = ({ tags }) => {
    if (!tags) return null;

    const seasonLabels = {
        "spring": "春",
        "summer": "夏",
        "autumn": "秋",
        "winter": "冬"
    };

    const dayTypeLabels = {
        "weekday": "平日",
        "weekend": "休日",
        "national_holiday": "祝日",
        "holiday_eve": "休日/祝日前"
    };

    const timePeriodLabels = {
        "morning": "朝",
        "noon": "午前中",
        "afternoon": "午後",
        "evening": "夕方",
        "late_night": "深夜"
    };

    return (
        <div className="flex flex-wrap gap-1 mb-1">
            {tags.date_range && (
                <div className="flex items-center border border-gray-300 px-2 py-0.5 rounded-full">
                    <CalendarDays size={12} className="mr-0.5" />
                    <span className="text-xs font-medium">{tags.date_range}</span>
                </div>
            )}

            {tags.season && (
                <div className="flex items-center border border-gray-300 px-2 py-0.5 rounded-full">
                    <Sun size={12} className="mr-0.5" />
                    <span className="text-xs font-medium">{seasonLabels[tags.season] || tags.season}</span>
                </div>
            )}

            {tags.day_type && (
                <div className="flex items-center border border-gray-300 px-2 py-0.5 rounded-full">
                    <CalendarDays size={12} className="mr-0.5" />
                    <span className="text-xs font-medium">{dayTypeLabels[tags.day_type] || tags.day_type}</span>
                </div>
            )}

            {tags.time_period && (
                <div className="flex items-center border border-gray-300 px-2 py-0.5 rounded-full">
                    <Clock size={12} className="mr-0.5" />
                    <span className="text-xs font-medium">{timePeriodLabels[tags.time_period] || tags.time_period}</span>
                </div>
            )}

            {tags.custom && tags.custom.length > 0 && tags.custom.map((tag, index) => (
                <div key={index} className="flex items-center border border-gray-300 px-2 py-0.5 rounded-full">
                    <Tag size={12} className="mr-0.5" />
                    <span className="text-xs font-medium">{tag}</span>
                </div>
            ))}
        </div>
    );
};

const MainContent = ({
    activeQuestion,
    interviewPlan = null,
    personaThoughts = [],
    expertProspections = [],
    // isStreamLoading = false,
    isStreamComplete = false,
    interviewPlanLoading = false,
    thoughtsLoading = false,
    prospectionsLoading = false,
    onPersonasAdded = null,
    targetPersonas = null,
    onEditMessage = null
}) => {
    return (
        <div className="flex-1 flex flex-col overflow-auto p-2 md:p-4 items-center">
            <div className="relative w-full mb-0.5">
                <h1 className="text-xl md:text-2xl text-left">{activeQuestion.text}</h1>
                {activeQuestion.tags && <TagDisplay tags={activeQuestion.tags} />}
            </div>
            <div className="relative w-full mb-1">
                {interviewPlan ? (
                    <InterviewPlan plan={interviewPlan} isLoading={interviewPlanLoading} />) : (<InterviewPlan plan={activeQuestion.interviewplan} isLoading={interviewPlanLoading} />)}
            </div>
            <div className="relative w-full flex-grow min-h-0">
                {personaThoughts.length > 0 ? (
                    <PersonaChat
                        thoughts={personaThoughts}
                        targetPersonas={targetPersonas || interviewPlan?.targetpersonas || activeQuestion.interviewplan?.targetpersonas}
                        isLoading={thoughtsLoading}
                        isComplete={isStreamComplete}
                        onPersonasAdded={onPersonasAdded}
                        onEditMessage={onEditMessage}
                    />
                ) : (
                    <PersonaChat
                        thoughts={activeQuestion.personas}
                        targetPersonas={targetPersonas || activeQuestion.interviewplan?.targetpersonas}
                        isLoading={thoughtsLoading}
                        isComplete={true}
                        onPersonasAdded={onPersonasAdded}
                        onEditMessage={onEditMessage}
                    />
                )}
            </div>

            {((expertProspections.length > 0) ||
                (activeQuestion.strategies?.length || activeQuestion.experts?.length)) && (
                    <div className="relative w-full mt-1">
                        <StrategySection
                            experts={expertProspections.length > 0 ? expertProspections : activeQuestion.experts}
                            isLoading={prospectionsLoading}
                        />
                    </div>
                )}

            {/* {isStreamLoading && !isStreamComplete && (
                <div className="mt-2 text-blue-500 animate-pulse text-center">
                    データ分析中...
                </div>
            )} */}
        </div>
    );
};

export default MainContent;