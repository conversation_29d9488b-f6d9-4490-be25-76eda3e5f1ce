import { useMemo, useState, useRef, useEffect } from 'react';
import PersonaProfileTooltip from './PersonaProfileTooltip';
import { getPersonaAvatar } from '../utils/iconMappings';
import { Plus, Check } from 'lucide-react';
import { personaProfiles } from '../data/personaProfiles';

const ChatPersonaHeader = ({ thoughts, targetPersonas = [], onPersonasAdded }) => {
    const [showDropdown, setShowDropdown] = useState(false);
    const [selectedPersonas, setSelectedPersonas] = useState([]);
    const [displayedPersonas, setDisplayedPersonas] = useState([]);
    const dropdownRef = useRef(null);

    useEffect(() => {
        if (targetPersonas && targetPersonas.length > 0) {
            const personas = targetPersonas.map(name => ({
                name,
                isInterviewer: name === "インタビュアー"
            }));
            if (!personas.some(p => p.isInterviewer)) {
                personas.unshift({
                    name: "インタビュアー",
                    isInterviewer: true
                });
            }
            setDisplayedPersonas(personas);
        } else if (thoughts && thoughts.length > 0) {
            const personaSet = new Set();
            const personas = [];
            thoughts.forEach(thought => {
                if (!personaSet.has(thought.name)) {
                    personaSet.add(thought.name);
                    personas.push({
                        name: thought.name,
                        isInterviewer: thought.name === "インタビュアー"
                    });
                }
            });
            setDisplayedPersonas(personas.sort((a, b) => {
                if (a.isInterviewer) return -1;
                if (b.isInterviewer) return 1;
                return 0;
            }));
        } else {
            setDisplayedPersonas([]);
        }
    }, [thoughts, targetPersonas]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const availablePersonas = useMemo(() => {
        const allPersonaNames = Object.keys(personaProfiles);
        return allPersonaNames
            .filter(name => !displayedPersonas.some(p => p.name === name))
            .map(name => ({ name }));
    }, [displayedPersonas]);

    const togglePersonaSelection = (persona) => {
        setSelectedPersonas(prev => {
            if (prev.some(p => p.name === persona.name)) {
                return prev.filter(p => p.name !== persona.name);
            } else {
                return [...prev, persona];
            }
        });
    };

    const addSelectedPersonas = () => {
        console.log('ChatPersonaHeader: addSelectedPersonas called');
        console.log('ChatPersonaHeader: Selected personas:', selectedPersonas);

        const selectedPersonaNames = selectedPersonas.map(p => p.name);
        console.log('ChatPersonaHeader: Selected persona names:', selectedPersonaNames);

        setDisplayedPersonas(prev => {
            console.log('ChatPersonaHeader: Current displayed personas:', prev);

            const newPersonas = [...prev];
            selectedPersonas.forEach(persona => {
                if (!newPersonas.some(p => p.name === persona.name)) {
                    newPersonas.push(persona);
                }
            });

            console.log('ChatPersonaHeader: Updated displayed personas:', newPersonas);

            if (onPersonasAdded && selectedPersonaNames.length > 0) {
                console.log('ChatPersonaHeader: Calling onPersonasAdded with:', selectedPersonaNames);
                console.log('ChatPersonaHeader: onPersonasAdded is a function?', typeof onPersonasAdded === 'function');

                setTimeout(() => {
                    console.log('ChatPersonaHeader: Executing onPersonasAdded callback');
                    onPersonasAdded(selectedPersonaNames);
                }, 0);
            } else {
                console.warn('ChatPersonaHeader: Not calling onPersonasAdded - either no callback provided or no personas selected');
                console.log('ChatPersonaHeader: onPersonasAdded exists?', !!onPersonasAdded);
                console.log('ChatPersonaHeader: selectedPersonaNames.length:', selectedPersonaNames.length);
            }

            return newPersonas;
        });

        setSelectedPersonas([]);
        setShowDropdown(false);
    };

    if (displayedPersonas.length === 0) return null;

    return (
        <div className="sticky top-0 bg-white border-b border-gray-200 p-1 z-10 shadow-sm">
            <div className="flex flex-wrap gap-1 items-center justify-center">
                {displayedPersonas.map((persona, index) => {
                    return (
                        <PersonaProfileTooltip
                            key={`${persona.name}-${index}`}
                            personaName={persona.name}
                        >
                            <div
                                className="flex items-center gap-1 border border-gray-300 text-gray-700 px-2 py-0.5 rounded-full transition-all hover:shadow hover:bg-gray-100 cursor-pointer"
                            >
                                <img src={getPersonaAvatar(persona.name)} alt={persona.name} className="w-4 h-4 rounded-full" />
                                <span className="text-xs font-medium">{persona.name}</span>
                            </div>
                        </PersonaProfileTooltip>
                    );
                })}

                <div className="relative" ref={dropdownRef}>
                    <button
                        className="flex items-center gap-1 border border-gray-300 text-gray-700 px-2 py-0.5 rounded-full transition-all hover:shadow hover:bg-gray-100 cursor-pointer"
                        onClick={() => setShowDropdown(!showDropdown)}
                    >
                        <Plus size={14} className="text-gray-500" />
                        <span className="text-xs font-medium">ペルソナを追加</span>
                    </button>

                    {showDropdown && availablePersonas.length > 0 && (
                        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-20 w-48">
                            <div className="p-2">
                                <div className="text-xs font-medium text-gray-500 mb-1 px-2">ペルソナを選択</div>
                                {availablePersonas.map((persona) => (
                                    <div
                                        key={persona.name}
                                        className={`flex items-center gap-2 px-2 py-1.5 rounded-md cursor-pointer ${selectedPersonas.some(p => p.name === persona.name)
                                            ? 'bg-blue-50'
                                            : 'hover:bg-gray-50'
                                            }`}
                                        onClick={() => togglePersonaSelection(persona)}
                                    >
                                        <div className="flex-shrink-0">
                                            {selectedPersonas.some(p => p.name === persona.name) ? (
                                                <Check size={14} className="text-blue-500" />
                                            ) : (
                                                <div className="w-3.5 h-3.5" />
                                            )}
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <img src={getPersonaAvatar(persona.name)} alt={persona.name} className="w-4 h-4 rounded-full" />
                                            <span className="text-xs">{persona.name}</span>
                                        </div>
                                    </div>
                                ))}

                                {selectedPersonas.length > 0 && (
                                    <div className="mt-2 pt-2 border-t border-gray-200 flex justify-end">
                                        <button
                                            className="px-2 py-1 bg-blue-400 text-white text-xs rounded-md hover:bg-blue-300"
                                            onClick={addSelectedPersonas}
                                        >
                                            追加する
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ChatPersonaHeader;