import React from 'react';
import { Plus } from 'lucide-react';

const Sidebar = ({ sortedQuestions, activeQuestionId, handleQuestionClick, addNewQuestion, isAddingNewQuestion }) => {

    return (
        <div className="w-full md:w-64 border-b md:border-b-0 md:border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200 flex items-center">
                <h2 className="text-lg font-medium">質問</h2>
                <button
                    className="ml-2 rounded-full bg-gray-100 p-1 hover:bg-gray-200 transition-colors"
                    onClick={addNewQuestion}
                >
                    <Plus size={18} />
                </button>
            </div>
            <div className="overflow-y-auto flex-1">
                {isAddingNewQuestion && (
                    <div
                        className="p-4 border-b border-gray-200 rounded-lg m-2 cursor-pointer bg-gray-100"
                    >
                        <p className="text-sm">新しい質問を作成中...</p>
                    </div>
                )}
                {sortedQuestions.map((question) => (
                    <div
                        key={question.id}
                        className={`p-4 border-b border-gray-200 rounded-lg m-2 cursor-pointer transition-colors ${question.id === activeQuestionId ? 'bg-gray-100' : 'hover:bg-gray-50'
                            }`}
                        onClick={() => handleQuestionClick(question.id)}
                    >
                        <p className="text-sm">{question.text}</p>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Sidebar;