import { useCallback } from 'react';

const InterviewPlan = ({ plan, isLoading }) => {
    const renderPlan = useCallback(() => {
        if (!plan) return null;

        return (
            <div className="text-gray-700 whitespace-pre-wrap">
                {plan.content}
            </div>
        );
    }, [plan]);

    return (
        <div className="w-full">
            <div className="bg-gray-50 border-l-4 border-blue-500 p-2 md:p-3 rounded-r-lg mb-2">
                <div className="text-blue-600 font-semibold text-sm mb-1">Interview Plan</div>
                <div className="text-sm">
                    {renderPlan()}
                </div>
                {isLoading && (
                    <div className="animate-pulse">
                        <div className="h-3 bg-blue-200 rounded w-3/4 mt-1"></div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default InterviewPlan;