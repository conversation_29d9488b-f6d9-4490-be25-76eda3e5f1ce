import { useState, useMemo } from 'react';
import { personaProfiles } from '../data/personaProfiles';

const PersonaProfileTooltip = ({ personaName, children }) => {
  const [isVisible, setIsVisible] = useState(false);

  const profile = useMemo(() => {
    return personaProfiles[personaName];
  }, [personaName]);

  if (!profile) {
    return children;
  }

  return (
    <div
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}

      {isVisible && (
        <div className="absolute z-50 w-72 bg-white rounded-lg shadow-lg border border-gray-200 p-4 text-left transform -translate-x-1/2 left-1/2 mt-2 animate-fadeIn">
          {/* Arrow */}
          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 rotate-45 bg-white border-t border-l border-gray-200"></div>

          <div className="flex items-start mb-3">
            <img
              src={profile.avatar}
              alt={personaName}
              className="w-12 h-12 rounded-full mr-3"
            />
            <div>
              <h3 className="font-bold text-lg">{personaName}</h3>
              {profile.age && (
                <p className="text-sm text-gray-600">{profile.age}歳 • {profile.gender}</p>
              )}
            </div>
          </div>

          <div className="space-y-2 text-sm">
            {profile.income && (
              <div>
                <span className="font-semibold text-gray-700">年収:</span> {profile.income}
              </div>
            )}

            {profile.occupation && (
              <div>
                <span className="font-semibold text-gray-700">職業:</span> {profile.occupation}
              </div>
            )}

            {profile.lifestyle && (
              <div>
                <span className="font-semibold text-gray-700">ライフスタイル:</span> {profile.lifestyle}
              </div>
            )}

            {profile.purchasePreferences && (
              <div>
                <span className="font-semibold text-gray-700">購入傾向:</span>
                <ul className="list-disc list-inside ml-2 mt-1">
                  {profile.purchasePreferences.slice(0, 2).map((pref, index) => (
                    <li key={index} className="text-xs">{pref}</li>
                  ))}
                </ul>
              </div>
            )}

            <div>
              <span className="font-semibold text-gray-700">概要:</span>
              <p className="text-xs mt-1">{profile.description}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PersonaProfileTooltip;
