import experienceIcon from '../assets/experience.png';

const iconMap = {
  '経営コンサルタント': experienceIcon,

};

const ExpertIcon = ({ title, className = '', size = 'normal' }) => {
  const icon = iconMap[title] || experienceIcon;
  const sizeClass = size === 'small' ? 'w-5 h-5' : 'w-6 h-6';

  return (
    <span className={`inline-flex items-center justify-center ${className}`}>
      <img
        src={icon}
        alt={title}
        className={`${sizeClass} object-contain`}
      />
    </span>
  );
};

export default ExpertIcon;
