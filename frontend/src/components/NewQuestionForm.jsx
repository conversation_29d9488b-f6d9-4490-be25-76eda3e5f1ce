import { useState, useEffect, useRef } from "react";
import { Utensils, CalendarDays, Sun, House, ArrowUp, Clock, Tag, X, Plus } from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const NewQuestionForm = ({ onSubmit }) => {
    const [questionText, setQuestionText] = useState('');
    const [isMounted, setIsMounted] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState('');
    const [showTagOptions, setShowTagOptions] = useState({
        date: false,
        season: false,
        day_type: false,
        time_period: false,
        custom: false
    });
    const [selectedTags, setSelectedTags] = useState({
        date_range: "",
        season: "",
        day_type: "",
        time_period: "",
        custom: []
    });

    const [dateRange, setDateRange] = useState([null, null]);
    const [customTagInput, setCustomTagInput] = useState("");
    const [dropdownPositions, setDropdownPositions] = useState({
        date: { top: 0, left: 0 },
        season: { top: 0, left: 0 },
        day_type: { top: 0, left: 0 },
        time_period: { top: 0, left: 0 }
    });
    const [tagsHeight, setTagsHeight] = useState(48); // Default height for tags area
    const customTagInputRef = useRef(null);
    const outsideClickHandlerRef = useRef(null);
    const tagsContainerRef = useRef(null);
    const dateButtonRef = useRef(null);
    const seasonButtonRef = useRef(null);
    const dayTypeButtonRef = useRef(null);
    const timePeriodButtonRef = useRef(null);

    useEffect(() => {
        setIsMounted(true);
        return () => {
            if (outsideClickHandlerRef.current) {
                document.removeEventListener('click', outsideClickHandlerRef.current);
            }
        };
    }, []);

    useEffect(() => {
        if (tagsContainerRef.current) {
            const tagsContainer = tagsContainerRef.current;

            const updateHeight = () => {
                const height = tagsContainer.offsetHeight;
                setTagsHeight(height);
            };

            updateHeight();

            const resizeObserver = new ResizeObserver(updateHeight);
            resizeObserver.observe(tagsContainer);

            return () => {
                resizeObserver.unobserve(tagsContainer);
                resizeObserver.disconnect();
            };
        }
    }, [selectedTags, showTagOptions.custom]);

    useEffect(() => {
        const updateDropdownPositions = () => {
            if (dateButtonRef.current) {
                const rect = dateButtonRef.current.getBoundingClientRect();
                setDropdownPositions(prev => ({
                    ...prev,
                    date: {
                        top: rect.bottom + window.scrollY + 5,
                        left: rect.left + window.scrollX
                    }
                }));
            }

            if (seasonButtonRef.current) {
                const rect = seasonButtonRef.current.getBoundingClientRect();
                setDropdownPositions(prev => ({
                    ...prev,
                    season: {
                        top: rect.bottom + window.scrollY + 5,
                        left: rect.left + window.scrollX
                    }
                }));
            }

            if (dayTypeButtonRef.current) {
                const rect = dayTypeButtonRef.current.getBoundingClientRect();
                setDropdownPositions(prev => ({
                    ...prev,
                    day_type: {
                        top: rect.bottom + window.scrollY + 5,
                        left: rect.left + window.scrollX
                    }
                }));
            }

            if (timePeriodButtonRef.current) {
                const rect = timePeriodButtonRef.current.getBoundingClientRect();
                setDropdownPositions(prev => ({
                    ...prev,
                    time_period: {
                        top: rect.bottom + window.scrollY + 5,
                        left: rect.left + window.scrollX
                    }
                }));
            }
        };

        window.addEventListener('resize', updateDropdownPositions);
        window.addEventListener('scroll', updateDropdownPositions);
        if (Object.values(showTagOptions).some(value => value)) {
            updateDropdownPositions();
        }

        return () => {
            window.removeEventListener('resize', updateDropdownPositions);
            window.removeEventListener('scroll', updateDropdownPositions);
        };
    }, [showTagOptions]);

    const handleSubmit = () => {
        if (questionText.trim()) {
            const tags = {};
            if (selectedTags.date_range) tags.date_range = selectedTags.date_range;
            if (selectedTags.season) tags.season = selectedTags.season;
            if (selectedTags.day_type) tags.day_type = selectedTags.day_type;
            if (selectedTags.time_period) tags.time_period = selectedTags.time_period;
            if (selectedTags.custom.length > 0) tags.custom = selectedTags.custom;
            onSubmit(questionText, { tags });
        }
    };

    const questionCategories = [
        "ご飯",
        "イベント",
        "トレンド",
        "ライフスタイル"
    ];

    const detailQuestionOptions = {
        "ご飯": [
            "子供が喜ぶメニューを教えて",
            "小腹がすいたときのおすすめは？",
            "時短メニューを教えて"
        ],
        "イベント": [
            "販促・集客イベントを企画したい",
            "ペット連れでも楽しめるイベントをしたい",
            "ファミリーに向けたイベントを企画したい"
        ],
        "トレンド": [
            "話題になっている商品は？",
            "SNSで人気の食品は？",
            "どんな特設ブースが欲しい？"
        ],
        "ライフスタイル": [
            "在宅勤務",
            "ダイエット中",
            "一人暮らし"
        ]
    };

    const handleCategoryClick = (category) => {
        if (selectedCategory === category) {
            setSelectedCategory('');
        } else {
            setSelectedCategory(category);
        }
    };

    const handleDetailQuestionClick = (question) => {
        setQuestionText(question);
        setSelectedCategory('');
    };

    const toggleTagOptions = (tagType) => {
        const updatePositionsForTag = () => {
            if (tagType === 'date' && dateButtonRef.current) {
                const rect = dateButtonRef.current.getBoundingClientRect();
                setDropdownPositions(prev => ({
                    ...prev,
                    date: {
                        top: rect.bottom + window.scrollY + 5,
                        left: rect.left + window.scrollX
                    }
                }));
            } else if (tagType === 'season' && seasonButtonRef.current) {
                const rect = seasonButtonRef.current.getBoundingClientRect();
                setDropdownPositions(prev => ({
                    ...prev,
                    season: {
                        top: rect.bottom + window.scrollY + 5,
                        left: rect.left + window.scrollX
                    }
                }));
            } else if (tagType === 'day_type' && dayTypeButtonRef.current) {
                const rect = dayTypeButtonRef.current.getBoundingClientRect();
                setDropdownPositions(prev => ({
                    ...prev,
                    day_type: {
                        top: rect.bottom + window.scrollY + 5,
                        left: rect.left + window.scrollX
                    }
                }));
            } else if (tagType === 'time_period' && timePeriodButtonRef.current) {
                const rect = timePeriodButtonRef.current.getBoundingClientRect();
                setDropdownPositions(prev => ({
                    ...prev,
                    time_period: {
                        top: rect.bottom + window.scrollY + 5,
                        left: rect.left + window.scrollX
                    }
                }));
            }
        };
        updatePositionsForTag();
        setShowTagOptions(prev => {
            if (prev[tagType]) {
                return {
                    ...prev,
                    [tagType]: false
                };
            }
            const newState = Object.keys(prev).reduce((acc, key) => {
                acc[key] = false;
                return acc;
            }, {});

            newState[tagType] = true;
            return newState;
        });

        setTimeout(() => {
            if (outsideClickHandlerRef.current) {
                document.removeEventListener('click', outsideClickHandlerRef.current);
            }
            const handleOutsideClick = (e) => {
                const dropdowns = document.querySelectorAll('.relative');
                let clickedOutside = true;

                dropdowns.forEach(dropdown => {
                    if (dropdown.contains(e.target)) {
                        clickedOutside = false;
                    }
                });

                if (clickedOutside) {
                    setShowTagOptions(prev =>
                        Object.keys(prev).reduce((acc, key) => {
                            acc[key] = false;
                            return acc;
                        }, {})
                    );
                    document.removeEventListener('click', handleOutsideClick);
                    outsideClickHandlerRef.current = null;
                }
            };
            outsideClickHandlerRef.current = handleOutsideClick;
            document.addEventListener('click', handleOutsideClick);
        }, 0);
    };

    const handleTagSelect = (tagType, value) => {
        setSelectedTags(prev => ({
            ...prev,
            [tagType]: value
        }));
        setShowTagOptions(prev => ({
            ...prev,
            [tagType]: false
        }));
    };

    const handleAddCustomTag = () => {
        if (customTagInput.trim()) {
            setSelectedTags(prev => ({
                ...prev,
                custom: [...prev.custom, customTagInput.trim()]
            }));
            setCustomTagInput("");

            if (customTagInputRef.current) {
                customTagInputRef.current.focus();
            }
        }
    };

    const handleRemoveTag = (tagType, value) => {
        if (tagType === 'custom') {
            setSelectedTags(prev => ({
                ...prev,
                custom: prev.custom.filter(tag => tag !== value)
            }));
        } else {
            setSelectedTags(prev => ({
                ...prev,
                [tagType]: ""
            }));
        }
    };

    const seasonOptions = ["spring", "summer", "autumn", "winter"];
    const seasonLabels = {
        "spring": "春",
        "summer": "夏",
        "autumn": "秋",
        "winter": "冬"
    };
    const dayTypeOptions = ["weekday", "weekend", "national_holiday", "holiday_eve"];
    const dayTypeLabels = {
        "weekday": "平日",
        "weekend": "休日",
        "national_holiday": "祝日",
        "holiday_eve": "休日/祝日前"
    };
    const timePeriodOptions = ["morning", "noon", "afternoon", "evening", "late_night"];
    const timePeriodLabels = {
        "morning": "朝",
        "noon": "午前中",
        "afternoon": "午後",
        "evening": "夕方",
        "late_night": "深夜"
    };

    const isSaveDisabled = questionText.trim() === '';

    return (
        <div className="flex-1 p-4 md:p-6 flex flex-col items-center justify-center">
            <div className="w-full max-w-3xl opacity-0 transition-opacity duration-500 ease-in-out text-center" style={{ opacity: isMounted ? 1 : 0 }}>
                <h2 className="text-xl font-medium mb-4">新しい質問を作成</h2>
                <div className="mb-4">
                    <div className="relative">
                        <textarea
                            className="w-full p-3 border border-gray-300 rounded-lg focus:border-gray-200"
                            style={{ paddingBottom: `${Math.max(24, tagsHeight + 16)}px` }}
                            rows="6"
                            value={questionText}
                            onChange={(e) => setQuestionText(e.target.value)}
                            placeholder="質問を入力してください"
                        />

                        <div className="absolute bottom-4 right-3 z-10">
                            <button
                                type="button"
                                className={`px-3 py-2 text-sm rounded-lg flex items-center justify-center text-white shadow-md
                                ${isSaveDisabled
                                        ? 'bg-blue-50 cursor-not-allowed'
                                        : 'bg-blue-400 text-white hover:bg-blue-300'
                                    }`}
                                disabled={isSaveDisabled}
                                onClick={handleSubmit}
                            >
                                <ArrowUp className="w-5 h-5" />
                            </button>
                        </div>

                        <div className="absolute bottom-3 left-3 right-16">
                            <div ref={tagsContainerRef} className="flex flex-wrap gap-2 w-full justify-center min-h-12 pb-1">
                                {/* Date Range Button */}
                                <div className="relative flex-shrink-0">
                                    <button
                                        ref={dateButtonRef}
                                        type="button"
                                        className={`px-3 transition-colors flex items-center rounded-full ${selectedTags.date_range
                                            ? 'bg-blue-50'
                                            : showTagOptions.date
                                                ? 'border border-blue-20'
                                                : 'border border-gray-200 hover:bg-gray-50'
                                            }`}
                                        style={{ height: '32px' }}
                                        onClick={() => {
                                            if (selectedTags.date_range) {
                                                handleRemoveTag('date_range');
                                            } else {
                                                toggleTagOptions('date');
                                            }
                                        }}
                                    >
                                        <CalendarDays size={14} className="mr-1.5 text-gray-500" />
                                        <span className="text-sm font-medium">
                                            {selectedTags.date_range || 'カレンダー'}
                                        </span>
                                        {selectedTags.date_range && (
                                            <X size={12} className="ml-1.5 text-gray-500" />
                                        )}
                                    </button>
                                </div>

                                {/* Season Button */}
                                <div className="relative flex-shrink-0">
                                    <button
                                        ref={seasonButtonRef}
                                        type="button"
                                        className={`px-3 transition-colors flex items-center rounded-full ${selectedTags.season
                                            ? 'bg-blue-50'
                                            : showTagOptions.season
                                                ? 'border border-gray-200'
                                                : 'border border-gray-200 hover:bg-gray-50'
                                            }`}
                                        style={{ height: '32px' }}
                                        onClick={() => {
                                            if (selectedTags.season) {
                                                handleRemoveTag('season');
                                            } else {
                                                toggleTagOptions('season');
                                            }
                                        }}>
                                        <Sun size={14} className="mr-1.5 text-gray-500" />
                                        <span className="text-sm font-medium">
                                            {selectedTags.season ? seasonLabels[selectedTags.season] : '季節'}
                                        </span>
                                        {selectedTags.season && (
                                            <X size={12} className="ml-1.5 text-gray-500" />
                                        )}
                                    </button>
                                </div>

                                {/* Day Type Button */}
                                <div className="relative flex-shrink-0">
                                    <button
                                        ref={dayTypeButtonRef}
                                        type="button"
                                        className={`px-3 transition-colors flex items-center rounded-full ${selectedTags.day_type
                                            ? 'bg-blue-50'
                                            : showTagOptions.day_type
                                                ? 'border border-gray-200'
                                                : 'border border-gray-200 hover:bg-gray-50'
                                            }`}
                                        style={{ height: '32px' }}
                                        onClick={() => {
                                            if (selectedTags.day_type) {
                                                handleRemoveTag('day_type');
                                            } else {
                                                toggleTagOptions('day_type');
                                            }
                                        }}
                                    >
                                        <CalendarDays size={14} className="mr-1.5 text-gray-500" />
                                        <span className="text-sm font-medium">
                                            {selectedTags.day_type ? dayTypeLabels[selectedTags.day_type] : '曜日帯'}
                                        </span>
                                        {selectedTags.day_type && (
                                            <X size={12} className="ml-1.5 text-gray-500" />
                                        )}
                                    </button>
                                </div>

                                {/* Time Period Button */}
                                <div className="relative flex-shrink-0">
                                    <button
                                        ref={timePeriodButtonRef}
                                        type="button"
                                        className={`px-3 transition-colors flex items-center rounded-full ${selectedTags.time_period
                                            ? 'bg-blue-50'
                                            : showTagOptions.time_period
                                                ? 'border border-gray-200'
                                                : 'border border-gray-200 hover:bg-gray-50'
                                            }`}
                                        style={{ height: '32px' }}
                                        onClick={() => {
                                            if (selectedTags.time_period) {
                                                handleRemoveTag('time_period');
                                            } else {
                                                toggleTagOptions('time_period');
                                            }
                                        }}
                                    >
                                        <Clock size={14} className="mr-1.5 text-gray-500" />
                                        <span className="text-sm font-medium">
                                            {selectedTags.time_period ? timePeriodLabels[selectedTags.time_period] : '時間帯'}
                                        </span>
                                        {selectedTags.time_period && (
                                            <X size={12} className="ml-1.5 text-gray-500" />
                                        )}
                                    </button>
                                </div>

                                {/* Custom tags display inline */}
                                {selectedTags.custom.map((tag, index) => (
                                    <div key={index} className="flex items-center gap-1 bg-blue-50 border px-3 rounded-full flex-shrink-0" style={{ height: '32px' }}>
                                        <Tag size={14} className="mr-1.5 text-gray-500" />
                                        <span className="text-sm font-medium">{tag}</span>
                                        <button
                                            type="button"
                                            className="ml-1.5 text-gray-400 hover:text-gray-600"
                                            onClick={() => handleRemoveTag('custom', tag)}
                                        >
                                            <X size={12} />
                                        </button>
                                    </div>
                                ))}
                                {showTagOptions.custom ? (
                                    <div className="flex items-center rounded-full bg-white border border-gray-300 px-2 flex-shrink-0" style={{ height: '32px' }}>
                                        <input
                                            ref={customTagInputRef}
                                            type="text"
                                            className="flex-1 px-1 text-sm bg-transparent border-none focus:outline-none h-full"
                                            placeholder="タグを入力"
                                            value={customTagInput}
                                            onChange={(e) => setCustomTagInput(e.target.value)}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter') {
                                                    e.preventDefault();
                                                    handleAddCustomTag();
                                                } else if (e.key === 'Escape') {
                                                    setShowTagOptions(prev => ({
                                                        ...prev,
                                                        custom: false
                                                    }));
                                                    setCustomTagInput("");
                                                }
                                            }}
                                            autoFocus
                                        />
                                        <button
                                            type="button"
                                            className="ml-1 h-full flex items-center justify-center text-gray-400 hover:text-gray-600"
                                            onClick={handleAddCustomTag}
                                        >
                                            <Plus size={14} />
                                        </button>
                                        <button
                                            type="button"
                                            className="ml-1 h-full flex items-center justify-center text-gray-400 hover:text-gray-600"
                                            onClick={() => {
                                                setShowTagOptions(prev => ({
                                                    ...prev,
                                                    custom: false
                                                }));
                                                setCustomTagInput("");
                                            }}
                                        >
                                            <X size={14} />
                                        </button>
                                    </div>
                                ) : (
                                    <button
                                        type="button"
                                        className="px-3 transition-colors flex items-center rounded-full border border-gray-200 hover:bg-gray-50 flex-shrink-0"
                                        style={{ height: '32px' }}
                                        onClick={() => toggleTagOptions('custom')}
                                    >
                                        <Plus size={14} className="mr-1.5 text-gray-500" />
                                        <span className="text-sm font-medium">キーワード</span>
                                    </button>
                                )}
                            </div>
                        </div>
                        {showTagOptions.date && (
                            <div className="fixed z-50 bg-white border border-gray-100 rounded-lg shadow-lg w-80"
                                style={{
                                    top: dropdownPositions.date.top,
                                    left: dropdownPositions.date.left
                                }}>
                                <div className="p-3">
                                    <h3 className="text-sm font-medium mb-2 text-gray-700">日付範囲を選択</h3>
                                    <div className="mb-2">
                                        <DatePicker
                                            selectsRange={true}
                                            startDate={dateRange[0]}
                                            endDate={dateRange[1]}
                                            onChange={(update) => {
                                                setDateRange(update);
                                                if (update[0] && update[1]) {
                                                    const formatDate = (date) => {
                                                        return date.toLocaleDateString('ja-JP', {
                                                            year: 'numeric',
                                                            month: '2-digit',
                                                            day: '2-digit'
                                                        });
                                                    };

                                                    const formattedStartDate = formatDate(update[0]);
                                                    const formattedEndDate = formatDate(update[1]);

                                                    handleTagSelect('date_range', `${formattedStartDate}～${formattedEndDate}`);
                                                    setTimeout(() => {
                                                        setShowTagOptions(prev => ({
                                                            ...prev,
                                                            date: false
                                                        }));
                                                    }, 500);
                                                }
                                            }}
                                            isClearable={true}
                                            placeholderText="クリックして日付を選択"
                                            className="w-full p-2 border border-gray-300 rounded-lg"
                                            dateFormat="yyyy/MM/dd"
                                            monthsShown={1}
                                        />
                                    </div>
                                    <div className="text-xs text-gray-600 mb-2">
                                        開始日と終了日を選択してください
                                    </div>
                                </div>
                            </div>
                        )}

                        {showTagOptions.season && (
                            <div className="fixed z-50 bg-white border border-gray-100 rounded-lg shadow-lg w-48"
                                style={{
                                    top: dropdownPositions.season.top,
                                    left: dropdownPositions.season.left
                                }}>
                                <div className="p-2">
                                    {seasonOptions.map((season) => (
                                        <button
                                            key={season}
                                            type="button"
                                            className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-md transition-colors"
                                            onClick={() => handleTagSelect('season', season)}
                                        >
                                            {seasonLabels[season]}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}

                        {showTagOptions.day_type && (
                            <div className="fixed z-50 bg-white border border-gray-100 rounded-lg shadow-lg w-48"
                                style={{
                                    top: dropdownPositions.day_type.top,
                                    left: dropdownPositions.day_type.left
                                }}>
                                <div className="p-2">
                                    {dayTypeOptions.map((dayType) => (
                                        <button
                                            key={dayType}
                                            type="button"
                                            className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-md transition-colors"
                                            onClick={() => handleTagSelect('day_type', dayType)}
                                        >
                                            {dayTypeLabels[dayType]}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}

                        {showTagOptions.time_period && (
                            <div className="fixed z-50 bg-white border border-gray-100 rounded-lg shadow-lg w-48"
                                style={{
                                    top: dropdownPositions.time_period.top,
                                    left: dropdownPositions.time_period.left
                                }}>
                                <div className="p-2">
                                    {timePeriodOptions.map((timePeriod) => (
                                        <button
                                            key={timePeriod}
                                            type="button"
                                            className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-md transition-colors"
                                            onClick={() => handleTagSelect('time_period', timePeriod)}
                                        >
                                            {timePeriodLabels[timePeriod]}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Question Categories */}
                <div className="relative mt-4 flex flex-row space-x-3 justify-center">
                    {questionCategories.map((category, index) => {
                        let Icon;
                        switch (category) {
                            case "ご飯":
                                Icon = Utensils;
                                break;
                            case "イベント":
                                Icon = CalendarDays;
                                break;
                            case "トレンド":
                                Icon = Sun;
                                break;
                            case "ライフスタイル":
                                Icon = House;
                                break;
                            default:
                                Icon = null;
                        }

                        return (
                            <button
                                key={index}
                                type="button"
                                className={`px-4 py-2 border rounded-full transition-colors flex items-center ${selectedCategory === category
                                    ? 'bg-gray-100 border-gray-100'
                                    : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                                    }`}
                                onClick={() => handleCategoryClick(category)}
                            >
                                {Icon && <Icon size={16} className="mr-2" />}
                                {category}
                            </button>
                        );
                    })}

                    {selectedCategory && (
                        <div
                            className="absolute z-50 left-1/2 transform -translate-x-1/2 mt-4 "
                            style={{
                                top: '100%',
                                width: '100%',
                                maxWidth: '600px'
                            }}
                        >
                            <div className="flex flex-col items-center space-y-2">
                                {detailQuestionOptions[selectedCategory].map((question, index) => (
                                    <button
                                        key={index}
                                        type="button"
                                        className="w-1/2 px-4 py-3 flex flex-row border border-gray-200 rounded-lg hover:bg-gray-50 text-align transition-colors text-center bg-white shadow-sm"
                                        onClick={() => handleDetailQuestionClick(question)}
                                    >
                                        {question}
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default NewQuestionForm;
