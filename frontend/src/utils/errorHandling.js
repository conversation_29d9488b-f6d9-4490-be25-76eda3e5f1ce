export const getErrorMessage = (error) => {
  const errorMessages = {
    "Network Error":
      "Network error. Please check your connection and try again.",
    400: "Invalid request format. Please check your input.",
    401: "Unauthorized. Please log in again.",
    403: "You don't have permission to access this resource.",
    404: "Resource not found.",
    500: "Server error. Please try again later.",
  };

  let errorKey = error.message;
  if (error.response?.status) {
    errorKey = error.response.status;
  }

  return (
    errorMessages[errorKey] || "An unexpected error occurred. Please try again."
  );
};
