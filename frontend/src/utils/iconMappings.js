// Expert icon mappings
export const expertIcons = {
  経営コンサルタント: "../assets/experience.png",
  マーケティングスペシャリスト: "../assets/marketing.png",
  データアナリスト: "../assets/analytics.png",
  顧客体験担当: "../assets/experience.png",
  マーケティングリサーチャー: "../assets/marketing.png",
};

import { personaProfiles } from "../data/personaProfiles";

export const getExpertIcon = (title) =>
  expertIcons[title] || "../assets/marketing.png";

export const getPersonaAvatar = (name) => {
  if (personaProfiles[name] && personaProfiles[name].avatar) {
    return personaProfiles[name].avatar;
  }
  return "/avatar1.png";
};

export const getAvatarByIndex = (index) => {
  const avatarIndex = (index % 11) + 1;
  return `/avatar${avatarIndex}.png`;
};
