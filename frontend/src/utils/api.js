const API_URL = "http://localhost:3001";

export const questionApi = {
  fetchAll: async () => {
    const response = await fetch(`${API_URL}/questions`);
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return response.json();
  },

  create: async (newQuestion) => {
    const response = await fetch(`${API_URL}/questions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(newQuestion),
    });

    if (!response.ok) {
      throw new Error("Failed to add question");
    }

    return response.json();
  },

  update: async (questionId, updatedData) => {
    const response = await fetch(`${API_URL}/questions/${questionId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updatedData),
    });

    if (!response.ok) {
      throw new Error("Failed to update question");
    }

    return response.json();
  },
};
