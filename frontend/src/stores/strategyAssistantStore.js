import { create } from "zustand";
import { persist } from "zustand/middleware";
import { toast } from "react-toastify";

const ACTIVE_QUESTION_KEY = "strategy-assistant-active-question";

// Default initial state uses null
const getDefaultState = () => ({
  activeQuestionId: null,
});

export const useStrategyAssistantStore = create(
  persist(
    (set) => ({
      ...getDefaultState(), // Initialize with default state
      setActiveQuestionId: (id) => set({ activeQuestionId: id }),
      resetActiveQuestionId: () => set(getDefaultState()), // Action to reset to default
    }),
    {
      name: ACTIVE_QUESTION_KEY, // Local storage key
      // Use default storage handling (localStorage)
      // Custom getItem can add robustness against invalid stored data
      storage: createJSONStorage(() => localStorage, {
        reviver: (key, value) => {
          // Optional: Add validation/migration logic here if needed
          // For example, ensure activeQuestionId is string or null
          if (
            key === "activeQuestionId" &&
            value !== null &&
            typeof value !== "string"
          ) {
            console.warn(
              `Invalid activeQuestionId type found in storage: ${typeof value}, resetting to null.`,
            );
            return null; // Reset if type is wrong
          }
          return value;
        },
      }),
      // Only persist activeQuestionId
      partialize: (state) => ({ activeQuestionId: state.activeQuestionId }),
      // Set version for potential future migrations
      version: 1,
      // Handle migration if storage version mismatches code version
      migrate: (persistedState, version) => {
        if (
          version === 0 &&
          persistedState?.activeQuestionId === "new-question-temp-id"
        ) {
          // Example migration: if old temp ID found, map to null
          return { ...persistedState, activeQuestionId: null };
        }
        // Add more migration logic here if needed for other versions
        return persistedState; // Default: no migration needed
      },
      // On hydration error (e.g., parsing failed), return default state
      onRehydrateStorage: () => {
        console.log("Hydration finished");
        return (error) => {
          if (error) {
            console.error("An error happened during hydration", error);
            // Return default state on error
            return getDefaultState();
          }
        };
      },
    },
  ),
);

// Helper function for default zustand storage creation
// In a real app, you might import this from zustand/middleware directly
// Added here for completeness based on potential zustand version differences
function createJSONStorage(getStorage, options) {
  let storage;
  try {
    storage = getStorage();
  } catch (e) {
    // prevent error loop if storage is not available
    toast.error("Could not load saved state. Starting fresh:", e);
    return;
  }
  const storageApi = {
    getItem: (name) => {
      const str = storage.getItem(name);
      if (!str) {
        return null;
      }
      const parsed = JSON.parse(str, options?.reviver);
      return parsed;
    },
    setItem: (name, value) => {
      storage.setItem(name, JSON.stringify(value, options?.replacer));
    },
    removeItem: (name) => {
      storage.removeItem(name);
    },
  };
  return storageApi;
}
