{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.74.7", "lucide-react": "^0.503.0", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-toastify": "^11.0.5", "styled-components": "^6.1.18", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.1", "zustand": "^5.0.4"}}